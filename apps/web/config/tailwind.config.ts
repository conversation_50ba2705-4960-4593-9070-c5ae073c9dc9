import type { Config } from "tailwindcss";
import path from "node:path";
import { fileURLToPath } from "node:url";

// Get absolute path to the project root (apps/web directory)
// This ensures paths work regardless of the current working directory
const getCurrentDir = () => {
  if (typeof __dirname !== 'undefined') {
    // CommonJS
    return path.dirname(__dirname); // Go up from config/ to apps/web/
  } else {
    // ESM
    const currentFile = fileURLToPath(import.meta.url);
    return path.dirname(path.dirname(currentFile)); // Go up from config/ to apps/web/
  }
};

const projectRoot = getCurrentDir();

// Convert relative paths to absolute paths from project root
const config = {
  content: [
    path.join(projectRoot, "pages/**/*.{ts,tsx}"),
    path.join(projectRoot, "components/**/*.{ts,tsx}"),
    path.join(projectRoot, "app/**/*.{ts,tsx}"),
    path.join(projectRoot, "src/**/*.{ts,tsx}"),
    path.join(projectRoot, "*.{js,ts,jsx,tsx,mdx}"),
  ],
  theme: {
    extend: {
      // Add any custom theme extensions here
    },
  },
  plugins: [],
} satisfies Config;

export default config;
