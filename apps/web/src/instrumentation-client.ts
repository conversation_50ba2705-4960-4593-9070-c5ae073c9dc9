/**
 * Client-side Sentry Instrumentation
 * 
 * This file contains client-side Sentry configuration that should be imported
 * in your main layout.tsx or _app.tsx file to initialize Sentry in the browser.
 * 
 * Location: src/instrumentation-client.ts (moved from config/ to src/ for proper organization)
 * Usage: Import this file in your root layout or app component
 */

import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // Set tracesSampleRate to 1.0 to capture 100%
  // of the transactions for performance monitoring.
  // We recommend adjusting this value in production
  tracesSampleRate: process.env.NODE_ENV === "production" ? 0.1 : 1.0,

  // Capture Replay for 10% of all sessions,
  // plus for 100% of sessions with an error
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,

  // Additional configuration for better error tracking
  beforeSend(event) {
    // Filter out development-only errors in production
    if (process.env.NODE_ENV === "development") {
      return event;
    }
    
    // Add custom filtering logic here if needed
    return event;
  },

  // Note: if you want to override the automatic release value, do not set a
  // `release` value here - use the environment variable `SENTRY_RELEASE`, so
  // that it will also get attached to your source maps
});

// Required for router navigation instrumentation in App Router
export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
