# CLAUDE.md - BuddyChip Ultimate Project Guide

## 🚀 Quick Start

```bash
# Install dependencies
pnpm install

# Development server (with Turbopack)
pnpm dev                              # Run web app
pnpm dev:web                         # Alternative command

# Build and deploy
pnpm build                           # Build for production
pnpm check-types                     # TypeScript type checking
pnpm start                           # Start production server

# To Check Supabase 
Use the Supabase MCP; ojxlpqerellfuiiidddj

# Code quality
pnpm lint                            # Lint with Biome
pnpm lint:fix                        # Fix linting issues
pnpm format                          # Format code
pnpm check                           # CI checks (Biome)
pnpm check:all                       # Full check (types + lint + tests)

# Testing
pnpm test                            # Run all tests with Vitest
pnpm test:run                        # Run tests once
pnpm test:watch                      # Watch mode
pnpm test:unit                       # Unit tests only
pnpm test:coverage                   # Coverage report
pnpm test:ui                         # Vitest UI

# AI & Telegram Bot
pnpm telegram:check-config           # Check Telegram bot configuration
```

## 🛠️ Technology Stack

### Core Stack
- **Package Manager**: pnpm (workspaces monorepo)
- **Monorepo Tool**: Turborepo with turbo.json
- **Runtime**: Node.js with Next.js 15 (App Router)
- **TypeScript**: Strict mode, ESNext modules
- **Styling**: TailwindCSS 4.1+ with shadcn/ui components

### Database & Backend
- **Database**: PostgreSQL with Prisma 6.9.0 ORM
- **Database Service**: Direct PostgreSQL + Supabase integration
- **Authentication**: Clerk with JWT validation
- **API Layer**: tRPC 11.4+ for type-safe APIs
- **Caching**: Vercel KV for rate limiting and sessions

### AI & Integrations
- **AI SDK**: Vercel AI SDK 4.3+ with streaming
- **Model Providers**: OpenRouter, OpenAI, xAI, Perplexity
- **Memory Storage**: Mem0 AI with Supabase backend
- **Telegram Bot**: node-telegram-bot-api with custom architecture
- **Twitter API**: TwitterAPI.io integration
- **Crypto Intelligence**: Cookie.fun API

### Development & Monitoring
- **Code Quality**: Biome (linting + formatting)
- **Testing**: Vitest with @testing-library
- **Monitoring**: Sentry with performance tracking
- **File Storage**: UploadThing
- **Build Tool**: Turbopack (dev) / Webpack (prod)

## 📁 Project Architecture

### Monorepo Structure
```
BuddyChipUtimate/
├── apps/
│   └── web/                         # Main Next.js application
│       ├── src/
│       │   ├── app/                 # App Router (Next.js 15)
│       │   ├── components/          # React components
│       │   ├── lib/                 # Core utilities & services
│       │   ├── routers/             # tRPC routers
│       │   ├── services/            # Business logic services
│       │   └── types/               # TypeScript definitions
│       ├── prisma/                  # Database schema & migrations
│       ├── config/                  # Configuration files
│       └── scripts/                 # Database & utility scripts
├── config/                          # Shared configuration (Biome)
├── docs/                           # Project documentation
└── turbo.json                      # Turborepo configuration
```

### Database Architecture (Prisma + Supabase)

**Dual Database Setup**:
- **Primary**: PostgreSQL with Prisma ORM (schema-first)
- **Secondary**: Supabase for AI memory storage and real-time features
- **Configuration**: Centralized in `/lib/prisma-config.ts`
- **Connection Management**: Singleton pattern in `/lib/db-utils.ts`

**Key Models**:
- `User` - Clerk-integrated user accounts
- `MonitoredAccount` - Twitter accounts being monitored
- `AIResponse` - Generated AI content with personas
- `TelegramUser` - Bot integration accounts
- `Memory` - AI memory storage (Mem0 integration)
- `PersonalityProfile` - AI personas and user-generated profiles

### Authentication System (Clerk)

- **Provider**: Clerk with JWT validation
- **Integration**: Full Next.js App Router support
- **Billing**: Custom billing integration with Clerk plans
- **Middleware**: CSRF protection and validation middleware
- **Security**: Rate limiting, input validation, audit logging

### AI Integration Architecture

**AI Providers**:
```typescript
// Model selection with fallbacks
const providers = {
  reasoning: "deepseek/deepseek-r1",           // Complex reasoning
  generation: "google/gemini-2.0-flash-exp",   // Content generation  
  conversation: "openai/gpt-4o-mini",         // General chat
  enhancement: "openai/o3-mini",              // Content improvement
};
```

**Services**:
- `BenjiAgent` - Main AI orchestration service
- `MemoryService` - Personalized AI memory (Mem0)
- `PersonaAnalyzer` - Twitter-based persona generation
- `ToolService` - AI tool coordination (search, image, etc.)

### Telegram Bot Architecture

**Modular Architecture** (Refactored from monolithic 1665+ lines):
```
lib/telegram/
├── core/                    # Bot instance management
│   ├── telegram-bot-core.ts       # Core bot & webhook handling
│   ├── telegram-bot-router.ts     # Message routing logic
│   └── telegram-handler-context.ts # Shared handler context
├── commands/               # Command handlers (/start, /help, etc.)
├── processors/            # Message processors (Twitter URLs, general)
├── callbacks/             # Inline keyboard callbacks
├── services/              # Business logic (user, session, security)
├── utils/                 # Utilities (logger, validator, formatter)
└── __tests__/            # Comprehensive test suite
```

**Handler Pattern**:
- **Commands**: `/start`, `/help`, `/settings`, `/status`, `/create`
- **Processors**: Twitter URL analysis, general conversation
- **Callbacks**: Copy, regenerate, enhance (o3 model) buttons

### Security Architecture

**Multi-layered Security** (Production-ready):
- **Rate Limiting**: Vercel KV-backed with sliding window
- **Input Validation**: Zod schemas with XSS/injection protection
- **CSRF Protection**: Token-based validation
- **Audit Logging**: Security event monitoring with Sentry
- **Access Control**: Clerk JWT + custom authorization
- **Security Dashboard**: Real-time threat monitoring at `/api/security/dashboard`

**Security Configurations**:
```typescript
const RATE_LIMITS = {
  API_GENERAL: { window: 60, limit: 60 },     // 60 req/min
  AI_GENERATION: { window: 60, limit: 10 },   // 10 AI calls/min
  TELEGRAM_MESSAGE: { window: 60, limit: 30 }, // 30 messages/min
};
```

## 🎯 Development Patterns

### Code Standards
- **TypeScript**: Strict mode, no `any` types
- **Indentation**: 2 spaces (Biome enforced)
- **Line Length**: 80 characters
- **Imports**: Type-only imports with `useImportType: "error"`
- **Error Handling**: Comprehensive error boundaries and logging

### Component Architecture
- **UI Components**: shadcn/ui with Radix primitives
- **State Management**: React Server Components + client state
- **Forms**: TanStack Form with Zod validation
- **Styling**: TailwindCSS with CVA (class-variance-authority)

### API Patterns
```typescript
// tRPC protected procedure pattern
export const userRouter = createTRPCRouter({
  getUserProfile: protectedProcedure
    .input(z.object({ userId: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.prisma.user.findUnique({
        where: { id: input.userId },
      });
    }),
});
```

### Database Patterns
```typescript
// Centralized Prisma client usage
import { prisma } from "@/lib/db-utils";

// Script utilities for database operations
import { runScript } from "@/lib/script-utils";
await runScript("migration-name", async (prisma) => {
  // Migration logic here
});
```

## 🔧 Configuration Files

### Key Configuration Files
- `turbo.json` - Monorepo build orchestration
- `config/biome.json` - Code quality rules
- `apps/web/tsconfig.json` - TypeScript configuration
- `apps/web/vitest.config.ts` - Testing configuration
- `apps/web/prisma/schema/schema.prisma` - Database schema

### Environment Variables (.env.example)
```env
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/buddychip
DIRECT_URL=postgresql://user:password@localhost:5432/buddychip

# Authentication
CLERK_SECRET_KEY=sk_test_...
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...

# AI Services
OPENROUTER_API_KEY=your_openrouter_api_key
OPENAI_API_KEY=your_openai_api_key
XAI_API_KEY=your_xai_api_key
PERPLEXITY_API_KEY=your_perplexity_api_key
EXA_API_KEY=your_exa_api_key

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret

# Infrastructure
KV_URL=your_upstash_kv_url
KV_TOKEN=your_upstash_kv_token
SENTRY_DSN=your_sentry_dsn
```

## 🚀 Deployment & Production

### Build Process
- **Development**: `pnpm dev` (Turbopack)
- **Production**: `pnpm build` (optimized build)
- **Type Safety**: `pnpm check-types` before deploy
- **Code Quality**: `pnpm check` (Biome CI mode)

### Environment-Specific Settings
- **Development**: Polling for Telegram bot
- **Production**: Webhook-based Telegram integration
- **Testing**: Mock services and test database

### Performance Optimization
- **React Server Components**: Default for optimal performance
- **Database**: Optimized indexes and connection pooling
- **AI Streaming**: Vercel AI SDK with streaming responses
- **Caching**: Vercel KV for rate limiting and sessions

## 🧪 Testing Strategy

### Test Types
- **Unit Tests**: Vitest with @testing-library
- **Integration Tests**: Full service integration
- **Performance Tests**: Telegram bot benchmarks
- **Security Tests**: Input validation and rate limiting

### Testing Commands
```bash
pnpm test                    # All tests
pnpm test:unit              # Unit tests only
pnpm test:watch             # Watch mode
pnpm test:coverage          # Coverage report
pnpm test:ui                # Vitest UI dashboard
```

### Test Configuration
- **Environment**: Node.js test environment
- **Database**: Separate test database
- **Mocking**: MSW for API mocking
- **Coverage**: 70% threshold for all metrics

## 🔍 Debugging & Monitoring

### Logging & Monitoring
- **Sentry**: Error tracking and performance monitoring
- **Custom Logging**: Structured logging with context
- **Database Logging**: Prisma query logging (configurable)
- **Security Auditing**: Comprehensive security event logging

### Debug Commands
```bash
# Environment validation
pnpm validate-env

# Telegram bot debugging
pnpm telegram:check-config

# Database health checks
pnpm db:studio

# Fix data consistency issues
pnpm fix:monitored-accounts
pnpm fix:user-accounts
```

## 📊 Performance Metrics

### Telegram Bot Performance
- **Command Processing**: <50ms average
- **Message Processing**: <40ms average
- **Callback Processing**: <30ms average
- **High Volume**: <10ms per update (100+ concurrent)

### Database Performance
- **Connection Pooling**: Optimized Prisma configuration
- **Index Strategy**: Comprehensive indexing for queries
- **Query Optimization**: Middleware-based monitoring

## 🛡️ Security Considerations

### Security Features
- **Input Validation**: Zod schemas with XSS protection
- **Rate Limiting**: Multi-tier rate limiting system
- **CSRF Protection**: Token-based protection
- **SQL Injection**: Parameterized queries with Prisma
- **Authentication**: Clerk JWT validation
- **Audit Logging**: Comprehensive security event tracking

### Security Checklist
- [ ] Environment variables configured
- [ ] Rate limiting tested and working
- [ ] CSRF protection enabled
- [ ] Input validation on all endpoints
- [ ] Security headers configured
- [ ] Sentry monitoring active
- [ ] SSL/TLS certificates valid

## 🚨 Common Issues & Solutions

### Known Issues (from MUSTFIX.md)
- **Security vulnerabilities**: Hard-coded tokens in some scripts
- **SQL injection risks**: String concatenation in database optimization
- **Rate limiting bugs**: Inconsistent member removal in Redis operations
- **Buffer compatibility**: Node.js Buffer usage in browser contexts
- **Test mocking**: Import path mismatches in Telegram tests

### Quick Fixes
```bash
# Fix type issues
pnpm check-types

# Fix linting issues  
pnpm lint:fix

# Reset database if corrupted
pnpm db:reset

# Check configuration
pnpm validate-env
```

## 📚 Additional Resources

- **Prisma Architecture**: `/apps/web/src/lib/PRISMA_ARCHITECTURE.md`
- **Telegram Bot Guide**: `/apps/web/src/lib/telegram/README.md`
- **Security Documentation**: `/SECURITY.md`
- **Issues to Fix**: `/docs/MUSTFIX.md`

## 🔗 External Documentation

- [Next.js 15 App Router](https://nextjs.org/docs/app)
- [Prisma ORM](https://www.prisma.io/docs)
- [tRPC](https://trpc.io/docs)
- [Clerk Authentication](https://clerk.com/docs)
- [Vercel AI SDK](https://sdk.vercel.ai/docs)
- [Telegram Bot API](https://core.telegram.org/bots/api)

## 💡 Development Tips

1. **Always run `pnpm check-types` before committing**
2. **Use `pnpm test:watch` during development**
3. **Check `pnpm db:studio` for database debugging**
4. **Monitor security dashboard in production**
5. **Use `pnpm lint:fix` to auto-fix code style**
6. **Run `pnpm check:all` for comprehensive validation**

---

**Last Updated**: 2025-08-18 | **Version**: 1.0.0 | **Architecture**: Production-Ready