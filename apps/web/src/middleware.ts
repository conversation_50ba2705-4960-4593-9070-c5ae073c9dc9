import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import { logSecurityEvent, validateRequestHeaders } from "./lib/security-utils";
import { addCSRFTokenToResponse } from "./lib/csrf-middleware";

// Define routes that should be protected (require authentication)
const isProtectedRoute = createRouteMatcher([
  "/trpc(.*)",
  "/api/webhooks/clerk(.*)", // Allow webhook to pass through
]);

// Define public routes that should bypass authentication
const isPublicRoute = createRouteMatcher([
  "/api/telegram/webhook(.*)", // Telegram webhook has its own security
  "/api/telegram/auth(.*)", // Telegram auth endpoints
  "/api/test-sentry(.*)", // Test endpoints
  "/sign-in(.*)",
  "/sign-up(.*)",
  "/",
]);

// Define allowed origins for CORS
const getAllowedOrigins = () => {
  const origins = process.env.ALLOWED_ORIGINS?.split(",") || [];
  if (process.env.NODE_ENV === "development") {
    origins.push("http://localhost:3000", "http://localhost:3001");
  }
  return origins;
};

export default clerkMiddleware(async (auth, req) => {
  const response = NextResponse.next();

  // Add security headers
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-XSS-Protection", "1; mode=block");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

  // Add CORS headers for API routes
  if (
    req.nextUrl.pathname.startsWith("/api/") ||
    req.nextUrl.pathname.startsWith("/trpc/")
  ) {
    const origin = req.headers.get("origin");
    const allowedOrigins = getAllowedOrigins();

    if (
      origin &&
      (allowedOrigins.includes(origin) || allowedOrigins.includes("*"))
    ) {
      response.headers.set("Access-Control-Allow-Origin", origin);
    }

    response.headers.set(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS"
    );
    response.headers.set(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization"
    );
    response.headers.set("Access-Control-Max-Age", "86400");
  }

  // Skip header validation for Telegram webhook (it has its own security)
  if (!req.nextUrl.pathname.startsWith("/api/telegram/")) {
    // Validate request headers for security
    const headerValidation = validateRequestHeaders(req.headers);
    if (!headerValidation.isValid) {
      console.warn(
        "🚨 Suspicious request headers detected:",
        headerValidation.issues
      );
      logSecurityEvent({
        type: "INVALID_INPUT",
        ip: req.headers.get("x-forwarded-for") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
        details: `Suspicious headers: ${headerValidation.issues.join(", ")}`,
      });
    }
  }

  // Skip authentication for public routes
  if (isPublicRoute(req)) {
    return response;
  }

  // For protected routes, ensure authentication
  if (isProtectedRoute(req)) {
    await auth.protect();
  }

  // Add CSRF tokens for authenticated users
  const finalResponse = await addCSRFTokenToResponse(response);
  return finalResponse;
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
};
